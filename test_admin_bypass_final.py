#!/usr/bin/env python3
"""
Test script to validate admin bypass functionality using the bypass_acl approach.
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_admin_bypass_logic():
    """Test the admin bypass logic in stream_chat_message"""
    print("Testing admin bypass logic...")
    
    try:
        from onyx.auth.users import is_user_admin
        from onyx.auth.schemas import UserRole
        
        # Mock user class for testing
        class MockUser:
            def __init__(self, email, role, user_id=1):
                self.email = email
                self.role = role
                self.id = user_id
        
        # Test 1: Admin user should return True for bypass
        admin_user = MockUser("<EMAIL>", UserRole.ADMIN)
        admin_bypass = is_user_admin(admin_user)
        
        if admin_bypass:
            print("✓ Admin user correctly triggers bypass_acl=True")
        else:
            print(f"✗ Admin user should trigger bypass, but got: {admin_bypass}")
            return False
        
        # Test 2: Team admin user should return False (no bypass)
        team_admin_user = MockUser("<EMAIL>", UserRole.TEAM_ADMIN)
        team_admin_bypass = is_user_admin(team_admin_user)
        
        if not team_admin_bypass:
            print("✓ Team admin user correctly does NOT trigger bypass_acl")
        else:
            print(f"✗ Team admin user should not trigger bypass, but got: {team_admin_bypass}")
            return False
        
        # Test 3: Basic user should return False (no bypass)
        basic_user = MockUser("<EMAIL>", UserRole.BASIC)
        basic_bypass = is_user_admin(basic_user)
        
        if not basic_bypass:
            print("✓ Basic user correctly does NOT trigger bypass_acl")
        else:
            print(f"✗ Basic user should not trigger bypass, but got: {basic_bypass}")
            return False
        
        # Test 4: None user should return False (no bypass)
        none_bypass = is_user_admin(None)
        
        if not none_bypass:
            print("✓ None user correctly does NOT trigger bypass_acl")
        else:
            print(f"✗ None user should not trigger bypass, but got: {none_bypass}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Admin bypass logic test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_team_access_still_works():
    """Test that team access still works for non-admin users"""
    print("\nTesting team access for non-admin users...")
    
    try:
        from onyx.access.access import _get_acl_for_user
        from onyx.access.utils import prefix_user_team, prefix_user_email
        from onyx.configs.constants import PUBLIC_DOC_PAT
        from onyx.auth.schemas import UserRole
        
        # Mock user and session
        class MockUser:
            def __init__(self, email, role, user_id=1):
                self.email = email
                self.role = role
                self.id = user_id
        
        # Mock get_user_team_ids function to return test team IDs
        def mock_get_user_team_ids(db_session, user_id):
            return [123, 456]  # Mock team IDs
        
        # Temporarily replace the function
        import onyx.access.access
        original_get_user_team_ids = onyx.access.access.get_user_team_ids
        onyx.access.access.get_user_team_ids = mock_get_user_team_ids
        
        try:
            # Test team admin user gets team ACL entries
            team_admin_user = MockUser("<EMAIL>", UserRole.TEAM_ADMIN)
            team_admin_acl = _get_acl_for_user(team_admin_user, None)
            
            expected_entries = {
                prefix_user_email(team_admin_user.email),
                PUBLIC_DOC_PAT,
                prefix_user_team(123),
                prefix_user_team(456)
            }
            
            if team_admin_acl == expected_entries:
                print("✓ Team admin user gets correct ACL entries including teams")
            else:
                print(f"✗ Team admin ACL mismatch. Expected: {expected_entries}, Got: {team_admin_acl}")
                return False
            
            # Test basic user gets team ACL entries
            basic_user = MockUser("<EMAIL>", UserRole.BASIC)
            basic_acl = _get_acl_for_user(basic_user, None)
            
            expected_basic_entries = {
                prefix_user_email(basic_user.email),
                PUBLIC_DOC_PAT,
                prefix_user_team(123),
                prefix_user_team(456)
            }
            
            if basic_acl == expected_basic_entries:
                print("✓ Basic user gets correct ACL entries including teams")
            else:
                print(f"✗ Basic user ACL mismatch. Expected: {expected_basic_entries}, Got: {basic_acl}")
                return False
            
            return True
            
        finally:
            # Restore original function
            onyx.access.access.get_user_team_ids = original_get_user_team_ids
        
    except Exception as e:
        print(f"✗ Team access test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("Running admin bypass tests (final implementation)...\n")
    
    tests = [
        test_admin_bypass_logic,
        test_team_access_still_works,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()  # Add spacing between tests
    
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Admin bypass functionality is working correctly.")
        print("\nSummary:")
        print("- Admin users will bypass ACL filtering and see all documents")
        print("- Team admin and basic users will use team-based access control")
        print("- Team access control is working for non-admin users")
        print("- No EE files were modified")
        return 0
    else:
        print("❌ Some tests failed. Please review the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
